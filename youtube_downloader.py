import yt_dlp
import sys
import os

# Configuration - update these values for your VPS
VPS_COOKIES_PATH = "/opt/you4elec_daily/www.youtube.com_cookies.txt"
OUTPUT_PATH = "/opt/you4elec_daily/output"
LINKS_FILE_PATH = "/opt/you4elec_daily/links.txt"  # Path to file containing URLs

def read_urls_from_file(file_path):
    """Read URLs from a text file, one URL per line."""
    try:
        with open(file_path, 'r') as file:
            urls = [line.strip() for line in file if line.strip()]
        return urls
    except Exception as e:
        print(f"Error reading URLs file: {e}")
        return []

def download_videos(urls, cookies_path=VPS_COOKIES_PATH, output_path=OUTPUT_PATH):
    """Download YouTube videos or playlists using cookies from a specified path."""
    options = {
        'format': 'best',
        'cookiefile': cookies_path,
        'outtmpl': f'{output_path}/%(playlist_title)s/%(title)s.%(ext)s',
        'verbose': True,
        'ignoreerrors': True,  # Skip videos that cannot be downloaded
        'extract_flat': False, # Extract full info for each video in playlist
        'yes_playlist': True,  # Download full playlists
    }
    
    print(f"Using cookies from: {cookies_path}")
    print(f"Saving to: {output_path}")
    
    with yt_dlp.YoutubeDL(options) as ydl:
        for url in urls:
            print(f"\nDownloading: {url}")
            try:
                ydl.download([url])
                print(f"Successfully downloaded: {url}")
            except Exception as e:
                print(f"Error downloading {url}: {e}")
    
    print("\nAll downloads complete!")

if __name__ == "__main__":
    # Create output directory if it doesn't exist
    os.makedirs(OUTPUT_PATH, exist_ok=True)
    
    # Command line arguments can override the links file
    if len(sys.argv) > 1:
        # If URL is provided directly, use it
        urls = [sys.argv[1]]
        
        # If a different links file is specified
        if len(sys.argv) > 2 and os.path.isfile(sys.argv[2]):
            urls = read_urls_from_file(sys.argv[2])
    else:
        # Read URLs from the default links file
        urls = read_urls_from_file(LINKS_FILE_PATH)
        
    if not urls:
        print(f"No URLs found. Please add URLs to {LINKS_FILE_PATH} or provide a URL as argument.")
        sys.exit(1)
        
    # Start downloads
    download_videos(urls, VPS_COOKIES_PATH, OUTPUT_PATH)

